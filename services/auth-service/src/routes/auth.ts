import { Router } from 'express';
import {
  validateBody,
  loginSchema,
  registerSchema,
  changePasswordSchema,
  refreshTokenSchema,
  authenticateToken,
  requireAdmin,
  asyncHandler,
} from '@crm/shared';
import * as authController from '../controllers/authController';

const router = Router();

// Public routes
router.post('/login', validateBody(loginSchema), asyncHandler(authController.login));
router.post('/refresh', validateBody(refreshTokenSchema), asyncHandler(authController.refreshToken));

// Protected routes
router.post('/logout', authenticateToken, asyncHandler(authController.logout));
router.get('/me', authenticateToken, asyncHandler(authController.getCurrentUser));
router.put('/change-password', 
  authenticateToken, 
  validateBody(changePasswordSchema), 
  asyncHand<PERSON>(authController.changePassword)
);

// Admin only routes
router.post('/register', 
  authenticateToken, 
  requireAdmin, 
  validateBody(registerSchema), 
  asyncHandler(authController.register)
);

export default router;
