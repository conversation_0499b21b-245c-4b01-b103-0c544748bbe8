import {
  ServiceResponse,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>word<PERSON>elper,
  LoginResponse,
  RegisterRequest,
  AuthenticatedUser,
  createServiceLogger,
} from '@crm/shared';
import { prisma, UserRole } from '@crm/database';

const logger = createServiceLogger('auth-service');

export const loginUser = async (
  email: string,
  password: string
): Promise<ServiceResponse<LoginResponse>> => {
  try {
    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return {
        success: false,
        message: 'Invalid email or password',
        statusCode: 401,
      };
    }

    if (!user.isActive) {
      return {
        success: false,
        message: 'Account is deactivated',
        statusCode: 401,
      };
    }

    // Verify password
    const isPasswordValid = await PasswordHelper.compare(password, user.password);
    if (!isPasswordValid) {
      return {
        success: false,
        message: 'Invalid email or password',
        statusCode: 401,
      };
    }

    // Generate tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    const { accessToken, refreshToken } = JWTHelper.generateTokenPair(tokenPayload);

    // Prepare user data (without password)
    const userData: AuthenticatedUser = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isActive: user.isActive,
    };

    return {
      success: true,
      data: {
        user: userData,
        accessToken,
        refreshToken,
      },
    };
  } catch (error) {
    logger.error('Login service error:', error);
    return {
      success: false,
      message: 'Login failed',
      statusCode: 500,
    };
  }
};

export const registerUser = async (
  userData: RegisterRequest
): Promise<ServiceResponse<AuthenticatedUser>> => {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email },
    });

    if (existingUser) {
      return {
        success: false,
        message: 'User with this email already exists',
        statusCode: 409,
      };
    }

    // Validate password strength
    const passwordValidation = PasswordHelper.validatePasswordStrength(userData.password);
    if (!passwordValidation.isValid) {
      return {
        success: false,
        message: passwordValidation.errors.join(', '),
        statusCode: 400,
      };
    }

    // Hash password
    const hashedPassword = await PasswordHelper.hash(userData.password);

    // Create user
    const newUser = await prisma.user.create({
      data: {
        email: userData.email,
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: (userData.role as UserRole) || UserRole.AGENT,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
      },
    });

    return {
      success: true,
      data: newUser,
    };
  } catch (error) {
    logger.error('Registration service error:', error);
    return {
      success: false,
      message: 'Registration failed',
      statusCode: 500,
    };
  }
};

export const refreshUserToken = async (
  refreshToken: string
): Promise<ServiceResponse<{ accessToken: string; refreshToken: string }>> => {
  try {
    // Verify refresh token
    const decoded = JWTHelper.verifyRefreshToken(refreshToken);

    // Check if user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
    });

    if (!user || !user.isActive) {
      return {
        success: false,
        message: 'Invalid refresh token',
        statusCode: 401,
      };
    }

    // Generate new tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    const tokens = JWTHelper.generateTokenPair(tokenPayload);

    return {
      success: true,
      data: tokens,
    };
  } catch (error) {
    logger.error('Token refresh service error:', error);
    return {
      success: false,
      message: 'Invalid refresh token',
      statusCode: 401,
    };
  }
};

export const changeUserPassword = async (
  userId: string,
  currentPassword: string,
  newPassword: string
): Promise<ServiceResponse<null>> => {
  try {
    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return {
        success: false,
        message: 'User not found',
        statusCode: 404,
      };
    }

    // Verify current password
    const isCurrentPasswordValid = await PasswordHelper.compare(
      currentPassword,
      user.password
    );

    if (!isCurrentPasswordValid) {
      return {
        success: false,
        message: 'Current password is incorrect',
        statusCode: 400,
      };
    }

    // Validate new password strength
    const passwordValidation = PasswordHelper.validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      return {
        success: false,
        message: passwordValidation.errors.join(', '),
        statusCode: 400,
      };
    }

    // Hash new password
    const hashedNewPassword = await PasswordHelper.hash(newPassword);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword },
    });

    return {
      success: true,
      data: null,
    };
  } catch (error) {
    logger.error('Change password service error:', error);
    return {
      success: false,
      message: 'Password change failed',
      statusCode: 500,
    };
  }
};
