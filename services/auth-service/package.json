{"name": "@crm/auth-service", "version": "1.0.0", "description": "Authentication service for CRM application", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"@crm/shared": "*", "@crm/database": "*", "express": "^4.18.0", "bcryptjs": "^2.4.3", "dotenv": "^16.3.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/bcryptjs": "^2.4.0", "@types/node": "^20.0.0", "nodemon": "^3.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "supertest": "^6.3.0", "@types/supertest": "^2.0.0", "ts-jest": "^29.0.0"}}