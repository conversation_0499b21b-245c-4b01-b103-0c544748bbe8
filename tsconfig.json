{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@crm/shared": ["./packages/shared/src"], "@crm/database": ["./packages/database"]}}, "include": [], "exclude": ["node_modules", "**/dist", "**/*.test.ts"], "references": [{"path": "./packages/shared"}, {"path": "./packages/database"}, {"path": "./services/api-gateway"}, {"path": "./services/auth-service"}, {"path": "./services/lead-service"}]}