#!/bin/bash

# Real Estate CRM Setup Script
echo "🏗️  Setting up Real Estate CRM Backend..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker and try again."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create logs directory
mkdir -p logs
echo "📁 Created logs directory"

# Create uploads directory
mkdir -p uploads
echo "📁 Created uploads directory"

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📄 Created .env file from .env.example"
    echo "⚠️  Please update the .env file with your configuration"
else
    echo "📄 .env file already exists"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm run install:all

# Start database services
echo "🐳 Starting database services..."
npm run docker:up

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npm run db:generate

# Push database schema
echo "🗄️  Pushing database schema..."
npm run db:push

# Seed database
echo "🌱 Seeding database..."
npm run db:seed

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Review and update the .env file with your configuration"
echo "2. Start the development servers: npm run dev"
echo "3. Access the API at: http://localhost:3000"
echo "4. View API documentation at: http://localhost:3000/api"
echo "5. Access pgAdmin at: http://localhost:5050 (<EMAIL> / admin123)"
echo ""
echo "🔐 Default login credentials:"
echo "Admin: <EMAIL> / admin123"
echo "Agent: <EMAIL> / agent123"
echo ""
