# Getting Started with Real Estate CRM

This guide will help you set up and run the Real Estate CRM backend application.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd real_state_crm_backend

# Run the setup script
chmod +x scripts/setup.sh
./scripts/setup.sh

# Start development servers
npm run dev
```

### Option 2: Manual Setup

1. **Install Dependencies**
   ```bash
   npm run install:all
   ```

2. **Set up Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Database Services**
   ```bash
   npm run docker:up
   ```

4. **Initialize Database**
   ```bash
   npm run db:generate
   npm run db:push
   npm run db:seed
   ```

5. **Start Services**
   ```bash
   npm run dev
   ```

## 🔧 Development Workflow

### Starting Development
```bash
# Start all services
npm run dev

# Or start individual services
npm run dev:gateway
npm run dev:auth
npm run dev:lead
# etc.
```

### Database Operations
```bash
# Generate Prisma client
npm run db:generate

# Push schema changes
npm run db:push

# Create migration
npm run db:migrate

# Seed database
npm run db:seed

# Open Prisma Studio
npm run db:studio
```

### Testing
```bash
# Run all tests
npm run test

# Run tests for specific service
npm run test --workspace=services/auth-service
```

## 📡 API Usage

### Base URLs
- **API Gateway**: `http://localhost:3000`
- **Direct Service Access**: `http://localhost:300X` (where X is service number)

### Authentication
1. **Login**
   ```bash
   curl -X POST http://localhost:3000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"admin123"}'
   ```

2. **Use Access Token**
   ```bash
   curl -X GET http://localhost:3000/api/leads \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
   ```

### Example API Calls

**Get All Leads**
```bash
curl -X GET "http://localhost:3000/api/leads?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Create a Lead**
```bash
curl -X POST http://localhost:3000/api/leads \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "source": "WEBSITE",
    "description": "Interested in downtown properties"
  }'
```

## 🗄️ Database Access

### pgAdmin
- **URL**: `http://localhost:5050`
- **Email**: `<EMAIL>`
- **Password**: `admin123`

### Direct PostgreSQL
```bash
# Connect to database
psql -h localhost -p 5432 -U crm_user -d crm_database
```

### Redis CLI
```bash
# Connect to Redis
redis-cli -h localhost -p 6379
```

## 🔍 Monitoring

### Health Checks
- **API Gateway**: `http://localhost:3000/health`
- **Auth Service**: `http://localhost:3001/health`
- **Lead Service**: `http://localhost:3002/health`
- And so on for each service...

### Logs
- Application logs are written to `logs/` directory
- Console output shows real-time service activity

## 🛠️ Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :3000
   
   # Kill process
   kill -9 <PID>
   ```

2. **Database Connection Failed**
   ```bash
   # Check if PostgreSQL is running
   docker ps
   
   # Restart database services
   npm run docker:down
   npm run docker:up
   ```

3. **Module Not Found Errors**
   ```bash
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm run install:all
   ```

4. **Prisma Client Issues**
   ```bash
   # Regenerate Prisma client
   npm run db:generate
   ```

### Reset Everything
```bash
# Stop all services
npm run docker:down

# Clean up
rm -rf node_modules package-lock.json
rm -rf services/*/node_modules
rm -rf packages/*/node_modules

# Reinstall and setup
npm run install:all
npm run docker:up
npm run db:generate
npm run db:push
npm run db:seed
```

## 📚 Next Steps

1. **Explore the API**: Visit `http://localhost:3000/api` for documentation
2. **Review the Code**: Start with `services/api-gateway/src/index.ts`
3. **Add Features**: Implement the remaining service endpoints
4. **Write Tests**: Add comprehensive test coverage
5. **Deploy**: Set up production environment

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Add tests
4. Update documentation
5. Submit a pull request

For more detailed information, see the main [README.md](README.md) file.
