import { Response } from 'express';
import { ApiResponse, SuccessResponse, ErrorResponse } from '../types/api';

export class ResponseHelper {
  static success<T>(
    res: Response,
    data: T,
    message?: string,
    statusCode: number = 200
  ): Response<SuccessResponse<T>> {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
      path: res.req.originalUrl,
    };

    return res.status(statusCode).json(response);
  }

  static error(
    res: Response,
    error: string,
    statusCode: number = 500,
    message?: string,
    details?: any
  ): Response<ErrorResponse> {
    const response: ErrorResponse = {
      success: false,
      error,
      message,
      statusCode,
      timestamp: new Date().toISOString(),
      path: res.req.originalUrl,
      details,
    };

    return res.status(statusCode).json(response);
  }

  static badRequest(
    res: Response,
    message: string = 'Bad Request',
    details?: any
  ): Response<ErrorResponse> {
    return this.error(res, 'BAD_REQUEST', 400, message, details);
  }

  static unauthorized(
    res: Response,
    message: string = 'Unauthorized'
  ): Response<ErrorResponse> {
    return this.error(res, 'UNAUTHORIZED', 401, message);
  }

  static forbidden(
    res: Response,
    message: string = 'Forbidden'
  ): Response<ErrorResponse> {
    return this.error(res, 'FORBIDDEN', 403, message);
  }

  static notFound(
    res: Response,
    message: string = 'Resource not found'
  ): Response<ErrorResponse> {
    return this.error(res, 'NOT_FOUND', 404, message);
  }

  static conflict(
    res: Response,
    message: string = 'Conflict'
  ): Response<ErrorResponse> {
    return this.error(res, 'CONFLICT', 409, message);
  }

  static validationError(
    res: Response,
    details: any,
    message: string = 'Validation failed'
  ): Response<ErrorResponse> {
    return this.error(res, 'VALIDATION_ERROR', 422, message, details);
  }

  static internalServerError(
    res: Response,
    message: string = 'Internal server error'
  ): Response<ErrorResponse> {
    return this.error(res, 'INTERNAL_SERVER_ERROR', 500, message);
  }
}
