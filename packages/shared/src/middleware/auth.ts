import { Request, Response, NextFunction } from 'express';
import { J<PERSON><PERSON><PERSON>per } from '../utils/jwt';
import { ResponseHelper } from '../utils/response';
import { AuthenticatedRequest } from '../types/api';
import { prisma } from '@crm/database';

export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      ResponseHelper.unauthorized(res, 'Access token required');
      return;
    }

    const decoded = JWTHelper.verifyAccessToken(token);
    
    // Fetch user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      ResponseHelper.unauthorized(res, 'Invalid or inactive user');
      return;
    }

    req.user = user;
    next();
  } catch (error) {
    ResponseHelper.unauthorized(res, 'Invalid access token');
  }
};

export const requireRole = (roles: string | string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(req.user.role)) {
      ResponseHelper.forbidden(res, 'Insufficient permissions');
      return;
    }

    next();
  };
};

export const requireAdmin = requireRole('ADMIN');
export const requireAgent = requireRole(['ADMIN', 'AGENT']);
