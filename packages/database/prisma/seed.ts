import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);

  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
    },
  });

  // Create agent users
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: await bcrypt.hash('agent123', 10),
      firstName: 'John',
      lastName: 'Smith',
      role: UserRole.AGENT,
    },
  });

  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: await bcrypt.hash('agent123', 10),
      firstName: 'Sarah',
      lastName: 'Johnson',
      role: UserRole.AGENT,
    },
  });

  console.log('✅ Database seeding completed successfully!');
  console.log(`👤 Admin user: <EMAIL> / admin123`);
  console.log(`👤 Agent 1: <EMAIL> / agent123`);
  console.log(`👤 Agent 2: <EMAIL> / agent123`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
