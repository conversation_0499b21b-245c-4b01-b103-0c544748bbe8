// Multi-Tenant Real Estate CRM Database Schema
// Supports multiple organizations with proper data isolation

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// CORE TENANT & USER MANAGEMENT
// ============================================================================

model Organization {
  id       String  @id @default(cuid())
  name     String
  slug     String  @unique
  domain   String? @unique
  logo     String?
  address  String?
  phone    String?
  email    String?
  website  String?
  timezone String  @default("UTC")
  currency String  @default("USD")

  // Subscription & billing
  subscriptionPlan   SubscriptionPlan   @default(STARTER)
  subscriptionStatus SubscriptionStatus @default(TRIAL)
  trialEndsAt        DateTime?
  subscriptionEndsAt DateTime?
  maxUsers           Int                @default(5)
  maxProperties      Int                @default(100)

  // Settings
  settings  Json?
  isActive  Boolean   @default(true)
  isDeleted Boolean   @default(false)
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  users          User[]
  properties     Property[]
  clients        Client[]
  deals          Deal[]
  activities     Activity[]
  communications Communication[]
  documents      Document[]
  customFields   CustomField[]
  dealPipelines  DealPipeline[]

  @@index([slug])
  @@index([isActive, isDeleted])
  @@map("organizations")
}

model User {
  id        String  @id @default(cuid())
  email     String  @unique
  password  String
  firstName String
  lastName  String
  avatar    String?
  phone     String?
  title     String?

  // Multi-tenant support
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Role & permissions
  role        UserRole     @default(AGENT)
  permissions Permission[]

  // Profile & preferences
  timezone    String?
  language    String  @default("en")
  preferences Json?

  // Security & status
  isActive    Boolean   @default(true)
  isVerified  Boolean   @default(false)
  lastLoginAt DateTime?
  isDeleted   Boolean   @default(false)
  deletedAt   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  assignedProperties Property[]      @relation("PropertyAssignee")
  ownedClients       Client[]        @relation("ClientOwner")
  assignedDeals      Deal[]          @relation("DealAssignee")
  createdActivities  Activity[]      @relation("ActivityCreator")
  assignedActivities Activity[]      @relation("ActivityAssignee")
  sentCommunications Communication[] @relation("CommunicationSender")
  createdDocuments   Document[]      @relation("DocumentCreator")
  auditLogs          AuditLog[]

  @@unique([email, organizationId])
  @@index([organizationId])
  @@index([isActive, isDeleted])
  @@map("users")
}

model Permission {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  resource String // e.g., "properties", "clients", "deals"
  action   String // e.g., "create", "read", "update", "delete"

  createdAt DateTime @default(now())

  @@unique([userId, resource, action])
  @@index([userId])
  @@map("permissions")
}

// ============================================================================
// PROPERTY MANAGEMENT
// ============================================================================

model Property {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Basic property information
  title        String
  description  String?
  propertyType PropertyType
  status       PropertyStatus @default(AVAILABLE)

  // Location
  address   String
  city      String
  state     String
  zipCode   String?
  country   String  @default("US")
  latitude  Float?
  longitude Float?

  // Property details
  bedrooms      Int?
  bathrooms     Float?
  squareMeters  Int?
  lotSizeMeters Float?
  yearBuilt     Int?

  // Pricing
  listPrice           Decimal? @db.Decimal(12, 2)
  salePrice           Decimal? @db.Decimal(12, 2)
  rentPrice           Decimal? @db.Decimal(12, 2)
  pricePerSquareMeter Decimal? @db.Decimal(8, 2)

  // Assignment
  assigneeId String?
  assignee   User?   @relation("PropertyAssignee", fields: [assigneeId], references: [id])

  // Features & amenities
  features  String[] @default([])
  amenities String[] @default([])

  // Media
  images         String[] @default([])
  virtualTourUrl String?

  // Listing details
  mlsNumber      String?
  listingDate    DateTime?
  expirationDate DateTime?
  daysOnMarket   Int?

  // Status tracking
  isActive   Boolean   @default(true)
  isFeatured Boolean   @default(false)
  isDeleted  Boolean   @default(false)
  deletedAt  DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  // Relations
  deals             Deal[]
  activities        Activity[]
  documents         Document[]
  customFieldValues CustomFieldValue[]

  @@index([organizationId])
  @@index([status])
  @@index([propertyType])
  @@index([isActive, isDeleted])
  @@index([city, state])
  @@index([assigneeId])
  @@map("properties")
}

// ============================================================================
// CLIENT MANAGEMENT
// ============================================================================

model Client {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Basic information
  firstName      String
  lastName       String
  email          String?
  phone          String?
  alternatePhone String?

  // Client type and status
  type   ClientType   @default(LEAD)
  status ClientStatus @default(ACTIVE)
  source LeadSource?

  // Address
  address String?
  city    String?
  state   String?
  zipCode String?
  country String? @default("US")

  // Assignment
  ownerId String
  owner   User   @relation("ClientOwner", fields: [ownerId], references: [id])

  // Preferences
  budget         Decimal?       @db.Decimal(12, 2)
  preferredAreas String[]       @default([])
  propertyTypes  PropertyType[] @default([])

  // Additional info
  notes String?
  tags  String[] @default([])

  // GDPR compliance
  consentGiven   Boolean   @default(false)
  consentDate    DateTime?
  marketingOptIn Boolean   @default(false)

  // Status tracking
  isActive  Boolean   @default(true)
  isDeleted Boolean   @default(false)
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  deals             Deal[]
  activities        Activity[]
  communications    Communication[]
  documents         Document[]
  customFieldValues CustomFieldValue[]

  @@index([organizationId])
  @@index([type])
  @@index([status])
  @@index([isActive, isDeleted])
  @@index([ownerId])
  @@index([email])
  @@map("clients")
}

// ============================================================================
// DEAL MANAGEMENT
// ============================================================================

model DealPipeline {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  name        String
  description String?
  isDefault   Boolean  @default(false)
  isActive    Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  stages DealStage[]
  deals  Deal[]

  @@index([organizationId])
  @@index([isDefault, isActive])
  @@map("deal_pipelines")
}

model DealStage {
  id         String       @id @default(cuid())
  pipelineId String
  pipeline   DealPipeline @relation(fields: [pipelineId], references: [id], onDelete: Cascade)

  name         String
  description  String?
  probability  Int      @default(0) // 0-100
  order        Int      @default(0)
  isClosedWon  Boolean  @default(false)
  isClosedLost Boolean  @default(false)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  deals Deal[]

  @@index([pipelineId])
  @@index([isActive])
  @@map("deal_stages")
}

model Deal {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Basic deal information
  title       String
  description String?
  value       Decimal? @db.Decimal(12, 2)

  // Pipeline & stage
  pipelineId String
  pipeline   DealPipeline @relation(fields: [pipelineId], references: [id])
  stageId    String
  stage      DealStage    @relation(fields: [stageId], references: [id])

  // Related entities
  clientId   String
  client     Client    @relation(fields: [clientId], references: [id])
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])

  // Assignment
  assigneeId String
  assignee   User   @relation("DealAssignee", fields: [assigneeId], references: [id])

  // Deal details
  dealType DealType    @default(SALE)
  priority Priority    @default(MEDIUM)
  source   LeadSource?

  // Important dates
  expectedCloseDate DateTime?
  actualCloseDate   DateTime?

  // Commission & fees
  commissionRate   Decimal? @db.Decimal(5, 4) // e.g., 0.0300 for 3%
  commissionAmount Decimal? @db.Decimal(12, 2)

  // Status tracking
  isActive  Boolean   @default(true)
  isDeleted Boolean   @default(false)
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  activities        Activity[]
  communications    Communication[]
  documents         Document[]
  customFieldValues CustomFieldValue[]

  @@index([organizationId])
  @@index([stageId])
  @@index([clientId])
  @@index([propertyId])
  @@index([assigneeId])
  @@index([dealType])
  @@index([isActive, isDeleted])
  @@map("deals")
}

// ============================================================================
// ACTIVITY & TASK MANAGEMENT
// ============================================================================

model Activity {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Basic information
  title       String
  description String?
  type        ActivityType   @default(TASK)
  status      ActivityStatus @default(PENDING)
  priority    Priority       @default(MEDIUM)

  // Assignment
  creatorId  String
  creator    User    @relation("ActivityCreator", fields: [creatorId], references: [id])
  assigneeId String?
  assignee   User?   @relation("ActivityAssignee", fields: [assigneeId], references: [id])

  // Related entities
  clientId   String?
  client     Client?   @relation(fields: [clientId], references: [id])
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])
  dealId     String?
  deal       Deal?     @relation(fields: [dealId], references: [id])

  // Scheduling
  dueDate   DateTime?
  startTime DateTime?
  endTime   DateTime?
  allDay    Boolean   @default(false)

  // Completion
  completedAt DateTime?
  completedBy String?

  // Status tracking
  isActive  Boolean   @default(true)
  isDeleted Boolean   @default(false)
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([organizationId])
  @@index([type])
  @@index([status])
  @@index([isActive, isDeleted])
  @@index([assigneeId])
  @@index([clientId])
  @@index([propertyId])
  @@index([dealId])
  @@index([dueDate])
  @@map("activities")
}

// ============================================================================
// COMMUNICATION MANAGEMENT
// ============================================================================

model Communication {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Basic information
  subject   String?
  content   String
  type      CommunicationType      @default(NOTE)
  direction CommunicationDirection @default(OUTBOUND)

  // Sender information
  senderId String
  sender   User   @relation("CommunicationSender", fields: [senderId], references: [id])

  // Related entities
  clientId String?
  client   Client? @relation(fields: [clientId], references: [id])
  dealId   String?
  deal     Deal?   @relation(fields: [dealId], references: [id])

  // Communication details
  scheduledAt DateTime?
  sentAt      DateTime?

  // Status tracking
  isActive  Boolean   @default(true)
  isDeleted Boolean   @default(false)
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([organizationId])
  @@index([type])
  @@index([direction])
  @@index([isActive, isDeleted])
  @@index([senderId])
  @@index([clientId])
  @@index([dealId])
  @@map("communications")
}

// ============================================================================
// DOCUMENT MANAGEMENT
// ============================================================================

model Document {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // File information
  name         String
  originalName String
  mimeType     String
  size         Int
  url          String

  // Document details
  type        DocumentType @default(OTHER)
  category    String?
  description String?

  // Upload information
  uploadedById String
  uploadedBy   User   @relation("DocumentCreator", fields: [uploadedById], references: [id])

  // Related entities
  clientId   String?
  client     Client?   @relation(fields: [clientId], references: [id])
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])
  dealId     String?
  deal       Deal?     @relation(fields: [dealId], references: [id])

  // Status tracking
  isActive  Boolean   @default(true)
  isDeleted Boolean   @default(false)
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@index([organizationId])
  @@index([type])
  @@index([isActive, isDeleted])
  @@index([uploadedById])
  @@index([clientId])
  @@index([propertyId])
  @@index([dealId])
  @@map("documents")
}

// ============================================================================
// CUSTOM FIELDS & AUDIT
// ============================================================================

model CustomField {
  id             String       @id @default(cuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  name       String
  label      String
  type       CustomFieldType @default(TEXT)
  entityType String // "client", "property", "deal"
  options    String[]        @default([]) // For SELECT, MULTI_SELECT
  isRequired Boolean         @default(false)
  isActive   Boolean         @default(true)
  order      Int             @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  values CustomFieldValue[]

  @@index([organizationId])
  @@index([entityType, isActive])
  @@map("custom_fields")
}

model CustomFieldValue {
  id            String      @id @default(cuid())
  customFieldId String
  customField   CustomField @relation(fields: [customFieldId], references: [id], onDelete: Cascade)

  // Entity references (only one should be set)
  clientId   String?
  client     Client?   @relation(fields: [clientId], references: [id], onDelete: Cascade)
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  dealId     String?
  deal       Deal?     @relation(fields: [dealId], references: [id], onDelete: Cascade)

  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([customFieldId])
  @@index([clientId])
  @@index([propertyId])
  @@index([dealId])
  @@map("custom_field_values")
}

model AuditLog {
  id             String  @id @default(cuid())
  organizationId String? // Nullable for system-level actions

  // Action details
  action     String // CREATE, UPDATE, DELETE, etc.
  entityType String // "user", "client", "property", "deal"
  entityId   String

  // User information
  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  // Change details
  oldValues Json?
  newValues Json?

  // Request context
  ipAddress  String?
  macAddress String?
  userAgent  String?

  createdAt DateTime @default(now())

  @@index([organizationId])
  @@index([entityType, entityId])
  @@index([userId])
  @@index([createdAt])
  @@map("audit_logs")
}

// ============================================================================
// ENUMS
// ============================================================================

// User & Organization Enums
enum UserRole {
  SUPER_ADMIN // Platform admin
  ADMIN // Organization admin
  MANAGER // Team manager
  AGENT // Real estate agent
  VIEWER // Read-only access
}

enum SubscriptionPlan {
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

enum SubscriptionStatus {
  TRIAL
  ACTIVE
  PAST_DUE
  CANCELLED
  EXPIRED
}

// Property Enums
enum PropertyType {
  SINGLE_FAMILY
  CONDO
  TOWNHOUSE
  MULTI_FAMILY
  LAND
  COMMERCIAL
  INDUSTRIAL
  RENTAL
  OTHER
}

enum PropertyStatus {
  AVAILABLE
  UNDER_CONTRACT
  SOLD
  RENTED
  OFF_MARKET
  COMING_SOON
  WITHDRAWN
}

// Client Enums
enum ClientType {
  LEAD
  PROSPECT
  BUYER
  SELLER
  TENANT
  LANDLORD
  INVESTOR
}

enum ClientStatus {
  ACTIVE
  INACTIVE
  CONVERTED
  LOST
  NURTURING
}

enum LeadSource {
  WEBSITE
  REFERRAL
  SOCIAL_MEDIA
  ADVERTISING
  COLD_CALL
  EMAIL_CAMPAIGN
  WALK_IN
  SIGN_CALL
  OPEN_HOUSE
  OTHER
}

// Deal Enums
enum DealType {
  SALE
  PURCHASE
  RENTAL
  LEASE
  INVESTMENT
}

// Activity Enums
enum ActivityType {
  TASK
  CALL
  EMAIL
  MEETING
  SHOWING
  OPEN_HOUSE
  FOLLOW_UP
  APPOINTMENT
  REMINDER
  OTHER
}

enum ActivityStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
  OVERDUE
}

// Communication Enums
enum CommunicationType {
  EMAIL
  PHONE
  SMS
  NOTE
  LETTER
  MEETING
  VIDEO_CALL
  OTHER
}

enum CommunicationDirection {
  INBOUND
  OUTBOUND
}

// Document Enums
enum DocumentType {
  CONTRACT
  LISTING_AGREEMENT
  PURCHASE_AGREEMENT
  DISCLOSURE
  INSPECTION_REPORT
  APPRAISAL
  PHOTO
  FLOOR_PLAN
  MARKETING_MATERIAL
  FINANCIAL_DOCUMENT
  LEGAL_DOCUMENT
  OTHER
}

// Custom Field Enums
enum CustomFieldType {
  TEXT
  TEXTAREA
  NUMBER
  DATE
  BOOLEAN
  SELECT
  MULTI_SELECT
  EMAIL
  PHONE
  URL
}

// General Enums
enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}
