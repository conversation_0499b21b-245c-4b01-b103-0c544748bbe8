# Real Estate CRM Backend

A comprehensive CRM application for real estate management built with modern technologies and microservices architecture.

## 🏗️ Architecture

This project follows a **microservices architecture within a monorepo** structure, providing:

- **Scalability**: Each service can be scaled independently
- **Maintainability**: Clear separation of concerns
- **Code Sharing**: Shared packages for common functionality
- **Development Efficiency**: Single repository for all services

## 🛠️ Technology Stack

- **Runtime**: Node.js/Bun with Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Caching & Queues**: Redis
- **Authentication**: JWT tokens
- **Validation**: Joi schemas
- **Logging**: Winston
- **Development**: TypeScript, Nodemon

## 📁 Project Structure

```
real_state_crm_backend/
├── packages/
│   ├── shared/           # Common utilities, middleware, types
│   └── database/         # Prisma schema and database utilities
├── services/
│   ├── api-gateway/      # Main entry point and routing
│   └── auth-service/     # Authentication and authorization
├── docker-compose.yml    # PostgreSQL and Redis containers
├── package.json          # Workspace configuration
└── README.md
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ or Bun
- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd real_state_crm_backend
   ```

2. **Install dependencies**
   ```bash
   npm run install:all
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start database services**
   ```bash
   npm run docker:up
   ```

5. **Set up the database**
   ```bash
   npm run db:generate
   npm run db:push
   npm run db:seed
   ```

6. **Start all services**
   ```bash
   npm run dev
   ```

## 🔧 Available Scripts

### Root Level Scripts

- `npm run dev` - Start all services in development mode
- `npm run build` - Build all services
- `npm run start` - Start all services in production mode
- `npm run test` - Run tests for all services
- `npm run docker:up` - Start PostgreSQL and Redis containers
- `npm run docker:down` - Stop containers
- `npm run install:all` - Install dependencies for all packages

### Database Scripts

- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with sample data

### Individual Service Scripts

- `npm run dev:gateway` - Start API Gateway only
- `npm run dev:auth` - Start Auth Service only
- `npm run dev:lead` - Start Lead Service only
- And so on for each service...

## 🌐 API Endpoints

### API Gateway (Port 3000)
- **Base URL**: `http://localhost:3000`
- **Health Check**: `GET /health`
- **API Documentation**: `GET /api`

### Service Routes (via API Gateway)
- **Authentication**: `/api/auth/*`

*Note: This is a simplified version focusing only on user management and authentication. Other services will be added in future iterations.*

## 🔐 Authentication

The system uses JWT-based authentication with:

- **Access Tokens**: Short-lived (15 minutes)
- **Refresh Tokens**: Long-lived (7 days)
- **Role-based Access Control**: Admin and Agent roles

### Default Users (after seeding)

- **Admin**: `<EMAIL>` / `admin123`
- **Agent 1**: `<EMAIL>` / `agent123`
- **Agent 2**: `<EMAIL>` / `agent123`

## 📊 Database Schema

The application currently includes:

- **Users**: System users with Admin/Agent roles

*Note: This simplified version focuses on user management and authentication. Additional entities (leads, contacts, properties, deals, etc.) will be added in future iterations.*

## 🔧 Development

### Adding a New Service

1. Create service directory in `services/`
2. Copy package.json template from existing service
3. Implement routes, controllers, and services
4. Add service to API Gateway proxy configuration
5. Update root package.json scripts

### Shared Package Development

The `@crm/shared` package contains:
- Common middleware (auth, validation, error handling)
- Utility functions (JWT, password hashing, Redis)
- Type definitions
- Validation schemas

### Database Changes

1. Update `packages/database/prisma/schema.prisma`
2. Run `npm run db:generate`
3. Run `npm run db:push` or create migration

## 🐳 Docker Support

The project includes Docker Compose configuration for:

- **PostgreSQL**: Database server
- **Redis**: Caching and session storage
- **pgAdmin**: Database administration interface

Access pgAdmin at `http://localhost:5050` with:
- Email: `<EMAIL>`
- Password: `admin123`

## 📝 Environment Variables

Key environment variables (see `.env.example`):

```env
# Database
DATABASE_URL="postgresql://crm_user:crm_password@localhost:5432/crm_database"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=15m

# Service Ports
API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
LEAD_SERVICE_PORT=3002
# ... other service ports
```

## 🧪 Testing

Run tests for all services:
```bash
npm run test
```

Run tests for specific service:
```bash
npm run test --workspace=services/auth-service
```

## 📈 Monitoring and Logging

- **Logging**: Winston logger with file and console output
- **Health Checks**: Each service exposes `/health` endpoint
- **Error Handling**: Centralized error handling middleware

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/api`
- Review the health status at `/health`
